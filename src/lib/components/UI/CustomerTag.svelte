<!-- CustomerTag.svelte -->
<script lang="ts">
	import { t } from '$src/lib/stores/i18n';
	import { enhance } from '$app/forms';
	import { Indicator, Button, Modal, Alert, Checkbox } from 'flowbite-svelte';
	import { EditOutline, CheckOutline } from 'flowbite-svelte-icons';
	import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';
	import { colorOptions, getColorClass } from '$lib/utils'; // adjust the path if needed

	// Expecting the current customer and the list of customer_tags as props.
	export let customer: any;
	export let customer_tags: any[];
	export let onRefresh: (() => Promise<void>) | undefined = undefined;
	// console.log('CustomerTag component initialized with customer:', customer);

	let customerAssignTagForm: HTMLFormElement;
	let customerAssignTagModalOpen = false;
	let currentcustomer: any = null;
	let selectedTagIds: (string | number)[] = [];

	let showSuccessMessage = false;
	let showErrorMessage = false;
	let successMessage = '';
	let errorMessage = '';

	// Open the modal and initialize with the current customer's tags if available.
	function opencustomerAssignTagModal(customer: any) {
		currentcustomer = { ...customer };

		if (currentcustomer.tags && Array.isArray(currentcustomer.tags)) {
			selectedTagIds = currentcustomer.tags
				.map((tag) => (typeof tag === 'object' ? tag.id : tag))
				.filter((id) => id !== undefined && id !== null && !isNaN(Number(id)));
		} else {
			selectedTagIds = [];
		}

		customerAssignTagModalOpen = true;
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
	}

	function handlecustomerAssigncustomer_tagsubmit(event: Event) {
		showSuccessMessage = false;
		showErrorMessage = false;
		successMessage = '';
		errorMessage = '';
	}

	$: enhanceOptions = {
		modalOpen: customerAssignTagModalOpen,
		setModalOpen: (value: boolean) => (customerAssignTagModalOpen = value),
		setShowSuccessMessage: (value: boolean) => (showSuccessMessage = value),
		setSuccessMessage: (value: string) => (successMessage = value),
		setShowErrorMessage: (value: boolean) => (showErrorMessage = value),
		setErrorMessage: (value: string) => (errorMessage = value),
		onSuccess: async () => {
			// Close the modal
			customerAssignTagModalOpen = false;

			// Call the refresh function if provided
			if (onRefresh) {
				await onRefresh();
			}
		}
	};

	$: tagOptions =
		customer_tags?.map((tag) => ({
			value: tag.id,
			name: `${tag.name}`,
			color: `${tag.color}`
		})) || [];

	// Debug logging
	// $: {
	//     console.log('CustomerTag component debug:');
	//     console.log('- customer_tags prop:', customer_tags);
	//     console.log('- tagOptions computed:', tagOptions);
	//     console.log('- tagOptions length:', tagOptions.length);
	// }
</script>

<!-- Button to open the assign tag modal -->
<Button id="edit-tag-button" type="button" color="blue" on:click={() => opencustomerAssignTagModal(customer)} data-testid="edit-tag-button">
	<EditOutline class="mr-2 h-4 w-4" />
	{t('edit_tag')}
</Button>

<!-- Modal for assigning customer_tags -->
<Modal id="assign-tag-modal" bind:open={customerAssignTagModalOpen} size="md" autoclose={true} class="w-full" data-testid="assign-tag-modal">
	<h2 id="assign-tag-modal-header" slot="header" class="text-sm" data-testid="assign-tag-modal-header">{t('assign_customer_tags')}</h2>
	{#if currentcustomer}
		{#if showSuccessMessage}
			<Alert color="green" class="mb-4">
				{successMessage}
			</Alert>
		{/if}
		{#if showErrorMessage}
			<Alert color="red" class="mb-4">
				{errorMessage}
			</Alert>
		{/if}
		<form
			id="assign-tag-form"
			bind:this={customerAssignTagForm}
			action="?/assign_customer_tag"
			method="POST"
			use:enhance={() => handleEnhance(enhanceOptions)}
			on:submit={handlecustomerAssigncustomer_tagsubmit}
			data-testid="assign-tag-form"
		>
			<input type="hidden" name="customer_id" value={customer.customer_id} />
			<div id="tag-selection-container" class="min-h-[200px]" data-testid="tag-selection-container">
				<label
					id="tag-selection-label"
					for="Selectedcustomer_tags"
					class="mb-1 block text-left text-sm font-medium text-gray-700"
					data-testid="tag-selection-label"
				>
					{t('select_customer_tags')}
				</label>
				{#if tagOptions.length > 0}
					<div id="tag-options-list" data-testid="tag-options-list">
						{#each tagOptions as tag (tag.value)}
							<div id="tag-option-{tag.value}" class="mb-2" data-testid="tag-option">
								<Checkbox
									id="tag-checkbox-{tag.value}"
									checked={selectedTagIds.includes(tag.value)}
									value={tag.value}
									on:change={() => {
										if (selectedTagIds.includes(tag.value)) {
											selectedTagIds = selectedTagIds.filter((id) => id !== tag.value);
										} else {
											selectedTagIds = [...selectedTagIds, tag.value];
										}
									}}
									data-testid="tag-checkbox-{tag.value}"
								>
									<Indicator size="lg" class={`mr-1 ${getColorClass(tag.color)}`} />
									{tag.name}
								</Checkbox>
							</div>
						{/each}
					</div>
				{:else}
					<div id="no-tags-available" class="py-4 text-sm text-gray-500" data-testid="no-tags-available">
						{t('no_tags_available')}
					</div>
				{/if}
				<input type="hidden" name="tag_ids[]" value={selectedTagIds} />
			</div>
		</form>
	{/if}
	<svelte:fragment slot="footer">
		<div id="assign-tag-modal-footer" data-testid="assign-tag-modal-footer">
			<Button id="assign-tag-update-button" color="green" on:click={() => customerAssignTagForm.requestSubmit()} data-testid="assign-tag-update-button"
				><CheckOutline class="mr-2 h-4 w-4" />
				{t('update')}</Button
			>
			<Button id="assign-tag-cancel-button" color="light" on:click={() => (customerAssignTagModalOpen = false)} data-testid="assign-tag-cancel-button"
				>{t('cancel')}</Button
			>
		</div>
	</svelte:fragment>
</Modal>
