import { test, expect } from '@playwright/test';

test.use({ viewport: { width: 1920, height: 1080 } });

test.describe('Chat Center Edit Tag End-to-End Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Clear any existing cookies and ensure fresh state
    await page.context().clearCookies();
    
    // Login first
    await page.goto('/login');
    await page.waitForTimeout(1000); // Small delay for page load
    await page.fill('input[name="username"]', 'admin');
    await page.waitForTimeout(1000);
    await page.fill('input[name="password"]', 'adminPW01!');
    await page.waitForTimeout(1000);
    await page.click('button[type="submit"]');
    
    // Wait for redirect to chat center
    await expect(page).toHaveURL('/chat_center');
    await page.waitForTimeout(1000); // Allow chat center to fully load
  });

  test('should complete full Edit Tag workflow in chat center', async ({ page }) => {
    // Step 1: Navigate to chat center page
    await page.goto('/chat_center');
    await expect(page).toHaveURL('/chat_center');
    
    // Verify page structure is loaded
    await expect(page.locator('h2:has-text("Chat Center")').first()).toBeVisible();
    
    // Step 2: Select "My Assigned" tab in PlatformIdentityList
    // Wait for tabs to be visible and try multiple selectors
    await page.waitForSelector('nav', { timeout: 10000 });
    await expect(page.locator('nav button').first()).toBeVisible();
    
    // Click on "My Assigned" tab - use ID selector first, fallback to first tab
    let myAssignedTab = page.locator('button[id="my-assigned"]').first();
    if (!(await myAssignedTab.isVisible())) {
      // Fallback to first tab (usually My Assigned)
      myAssignedTab = page.locator('nav button').first();
    }
    await myAssignedTab.click();
    await page.waitForTimeout(1000); // Allow tab content to load
    
    // Wait for chat items to load with multiple selectors
    await page.waitForSelector('.divide-y button, [data-testid="chat-item"]', { timeout: 15000 });
    
    // Step 3: Select the first available chat item from the list
    const chatItems = page.locator('.divide-y button');
    await expect(chatItems.first()).toBeVisible();
    
    // Store the chat item for later reference
    const firstChatItem = chatItems.first();
    await firstChatItem.click();
    await page.waitForTimeout(2000); // Allow conversation to load
    
    // Step 4: Verify ConversationView loads with customer information
    // Wait for conversation header to be visible
    await expect(page.locator('[data-testid="conversation-header"]').or(page.locator('.min-h-\\[125px\\]')).first()).toBeVisible();
    
    // Verify customer name is displayed
    await expect(page.locator('h2').filter({ hasText: /\w+/ }).first()).toBeVisible();
    
    // Step 5: Navigate to Information tab in CustomerInfoPanel
    // Look for tab navigation and click on Information tab
    const informationTab = page.locator('button').filter({ hasText: /Information|information/ }).first();
    
    // If Information tab is not visible, look for tab buttons and click second one (usually Information)
    if (!(await informationTab.isVisible())) {
      const tabButtons = page.locator('[role="tablist"] button, .tab-button, nav button').filter({ hasText: /^(?!My|Chat)/ });
      if (await tabButtons.count() > 0) {
        await tabButtons.first().click();
      }
    } else {
      await informationTab.click();
    }
    
    await page.waitForTimeout(1000); // Allow tab content to load
    
    // Step 6: Locate and click the "Edit Tag" button in InformationTab
    // Wait for the Edit Tag button to be visible
    const editTagButton = page.locator('button').filter({ hasText: /Edit Tag|edit_tag/ }).first();
    await expect(editTagButton).toBeVisible({ timeout: 10000 });
    await editTagButton.click();
    await page.waitForTimeout(1000); // Wait for modal to open
    
    // Step 7: Verify that the CustomerTag modal opens correctly
    // Check for modal visibility using multiple selectors
    const tagModal = page.locator('div[role="dialog"]').or(page.locator('.modal')).or(page.locator('h2').filter({ hasText: /assign.*tag|tag/i }));
    await expect(tagModal.first()).toBeVisible();
    
    // Verify modal header shows assign/edit tag text
    await expect(page.locator('h2').filter({ hasText: /assign.*tag|tag/i })).toBeVisible();
    
    // Step 8: Select a specific tag and capture its name for verification
    // Wait for checkboxes to be visible
    await page.waitForSelector('input[type="checkbox"]', { timeout: 10000 });
    
    // Use role-based selectors since Playwright recognizes the tag names
    const tagNames = ['At-Risk', 'High-Value', 'New', 'Promotional', 'Returning', 'VIP'];
    
    // Find the first unchecked tag to ensure we're adding a tag
    let selectedTagName = '';
    let selectedCheckbox = null;
    
    for (const tagName of tagNames) {
      const checkbox = page.getByRole('checkbox', { name: tagName });
      
      if (await checkbox.isVisible()) {
        const isChecked = await checkbox.isChecked();
        
        if (!isChecked) {
          selectedTagName = tagName;
          selectedCheckbox = checkbox;
          break;
        }
      }
    }
    
    // If no unchecked tags found, select the first available one
    if (!selectedCheckbox) {
      for (const tagName of tagNames) {
        const checkbox = page.getByRole('checkbox', { name: tagName });
        
        if (await checkbox.isVisible()) {
          selectedTagName = tagName;
          selectedCheckbox = checkbox;
          break;
        }
      }
    }
    
    expect(selectedTagName).toBeTruthy(); // Ensure we captured a tag name
    expect(selectedCheckbox).toBeTruthy(); // Ensure we found a checkbox
    console.log(`Selecting tag: "${selectedTagName}"`);
    
    // Get the current state and toggle it
    const isCurrentlyChecked = await selectedCheckbox!.isChecked();
    
    // Click to toggle the checkbox
    await selectedCheckbox!.click();
    await page.waitForTimeout(500); // Small delay for UI update
    
    // Verify the checkbox state changed
    const isNowChecked = await selectedCheckbox!.isChecked();
    expect(isNowChecked).toBe(!isCurrentlyChecked);
    
    // Step 9: Click the "Update" button to save changes
    const updateButton = page.locator('button').filter({ hasText: /Update|update/ }).first();
    await expect(updateButton).toBeEnabled(); // Ensure button is enabled before clicking
    await updateButton.click();
    await page.waitForTimeout(1000); // Wait for save action
    
    // Step 10: Verify the modal closes after successful update
    await expect(tagModal.first()).not.toBeVisible({ timeout: 10000 });
    
    // Optional: Check for success message/toast
    const successToast = page.locator('[role="alert"]').filter({ hasText: /success|updated/i });
    if (await successToast.isVisible()) {
      console.log('Success message detected');
    }
    
    // Step 11: Verify that the specific selected tag appears in the InformationTab
    // Wait a moment for the UI to update
    await page.waitForTimeout(2000);
    
    // Check if the tag was added or removed based on the previous state
    if (isNowChecked && !isCurrentlyChecked) {
      // Tag was added - verify it appears in the Information tab
      console.log(`Verifying that tag "${selectedTagName}" appears in Information tab...`);
      
      // Look for the specific tag name in tag indicators with multiple selector strategies
      // Try different selectors that might match the tag display
      const selectorStrategies = [
        `span.tag:has-text("${selectedTagName}")`,
        `span[class*="tag"]:has-text("${selectedTagName}")`, 
        `span:has-text("${selectedTagName}")`
      ];
      
      let specificTagIndicator = null;
      
      for (const selector of selectorStrategies) {
        specificTagIndicator = page.locator(selector);
        const count = await specificTagIndicator.count();
        if (count > 0) {
          console.log(`Found tag using selector: ${selector}`);
          break;
        }
      }
      
      // If no specific selector works, let's debug what's actually on the page
      if (!specificTagIndicator || await specificTagIndicator.count() === 0) {
        console.log('Debug: Looking for all tag elements...');
        const allTagElements = page.locator('span.tag, span[class*="tag"]');
        const allTagCount = await allTagElements.count();
        console.log(`Found ${allTagCount} tag elements total`);
        
        if (allTagCount > 0) {
          for (let i = 0; i < allTagCount; i++) {
            const tagText = await allTagElements.nth(i).textContent();
            console.log(`Tag ${i}: "${tagText}"`);
          }
        }
        
        // Fallback: look for any span containing the tag name
        specificTagIndicator = page.locator('span').filter({ hasText: selectedTagName });
      }
      
      await expect(specificTagIndicator.first()).toBeVisible({ timeout: 10000 });
      console.log(`✓ Tag "${selectedTagName}" successfully appears in Information tab`);
      
    } else if (!isNowChecked && isCurrentlyChecked) {
      // Tag was removed - verify it no longer appears in the Information tab
      console.log(`Verifying that tag "${selectedTagName}" is removed from Information tab...`);
      
      // Look for the specific tag name in tag indicators - should not be visible
      const specificTagIndicator = page.locator('span[class*="tag"]').filter({ hasText: selectedTagName });
      
      // Wait a bit to ensure UI has updated, then check it's not visible
      await page.waitForTimeout(1000);
      const isTagVisible = await specificTagIndicator.isVisible();
      
      if (isTagVisible) {
        console.log(`⚠ Tag "${selectedTagName}" still appears in Information tab - this might indicate the removal didn't work`);
      } else {
        console.log(`✓ Tag "${selectedTagName}" successfully removed from Information tab`);
      }
    } else {
      // No state change occurred - this might happen if checkbox was already in the target state
      console.log(`No tag state change detected for "${selectedTagName}" - checkbox remained ${isNowChecked ? 'checked' : 'unchecked'}`);
      
      // Still verify the tag visibility matches the checkbox state
      const specificTagIndicator = page.locator('span[class*="tag"]').filter({ hasText: selectedTagName });
      const isTagVisible = await specificTagIndicator.isVisible();
      
      if (isNowChecked) {
        await expect(specificTagIndicator).toBeVisible();
        console.log(`✓ Tag "${selectedTagName}" correctly appears in Information tab (was already selected)`);
      } else {
        expect(isTagVisible).toBe(false);
        console.log(`✓ Tag "${selectedTagName}" correctly does not appear in Information tab (was already unselected)`);
      }
    }
    
    // Step 12: Additional verification - ensure the chat item is still selected
    await expect(firstChatItem).toHaveClass(/bg-blue-100|selected|active/);
    
    console.log('Edit Tag workflow completed successfully');
    
    // Keep browser open for debugging if needed
    await page.pause();
  });
});