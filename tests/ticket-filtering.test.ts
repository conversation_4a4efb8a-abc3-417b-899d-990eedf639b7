/**
 * End-to-End Test: Ticket Filtering - View My Tickets Functionality
 *
 * This comprehensive test validates the ticket filtering functionality on the monitoring
 * page, specifically testing the "View My Tickets" button that filters tickets to show
 * only those assigned to the currently logged-in user.
 *
 * SVELTEKIT PAGES TESTED:
 * - /monitoring (+page.svelte) - Main ticket monitoring and management dashboard
 *   └── Loads ticket data via +page.server.ts load function with filtering support
 *   └── Integrates TicketTable.svelte component for ticket display
 *   └── Implements "View My Tickets" button with toggleViewMyTasks() function
 *
 * SVELTE COMPONENTS TESTED:
 * - TicketTable.svelte - Ticket management table with sortable columns and ticket display
 *   └── Renders ticket data with agent information and various ticket properties
 *   └── Uses dynamic IDs for each ticket row and cell for testing purposes
 *   └── Provides data-testid attributes for reliable test selectors
 *
 * COMPLETE WORKFLOW TESTED:
 * 1. Authentication and navigation to monitoring page (/monitoring)
 * 2. Page verification and initial ticket data loading
 * 3. Current user identification from sidebar profile
 * 4. Initial ticket state capture (before filtering)
 * 5. "View My Tickets" button activation and state verification
 * 6. Filtered ticket data validation (ensuring only current user tickets)
 * 7. Filter toggle functionality testing (on/off states)
 * 8. Empty filter results handling
 *
 * DATA FLOW AND INTEGRATION POINTS:
 * - SvelteKit server-side data loading with filtering parameters
 * - TicketService API integration for filtered ticket retrieval
 * - Real-time UI updates based on filter state changes
 * - Cross-component communication between page and TicketTable component
 *
 * ID SELECTOR STRATEGY:
 * All ID selectors reference actual HTML elements defined in the respective
 * Svelte components. Each selector is documented with its source component
 * and line number for maintainability. Data-testid attributes are used
 * as primary selectors for stability across UI changes.
 */

import { test, expect } from '@playwright/test';

test.use({ viewport: { width: 1920, height: 1080 } });

test.describe('Ticket Page Filtering - View My Tickets E2E Flow', () => {
	test.beforeEach(async ({ page }) => {
		// Clear any existing cookies and ensure fresh state
		await page.context().clearCookies();

		// Login first
		await page.goto('/login');
		await page.waitForTimeout(1000); // Small delay for page load
		await page.fill('input[name="username"]', 'admin');
		await page.waitForTimeout(1000);
		await page.fill('input[name="password"]', 'adminPW01!');
		await page.waitForTimeout(1000);
		await page.click('button[type="submit"]');

		// Wait for redirect to chat center
		await expect(page).toHaveURL('/chat_center');
		await page.waitForTimeout(1000); // Allow chat center to fully load
	});

	test('should filter tickets to show only current user tickets when View My Tickets is enabled', async ({
		page
	}) => {
		/**
		 * PHASE 1: Authentication & Setup (completed in beforeEach)
		 *
		 * Authentication is handled in the beforeEach hook, ensuring a clean
		 * session state and successful login before each test execution.
		 */
		console.log('Phase 1: Authentication completed');

		/**
		 * PHASE 2: Sidebar Navigation to Monitoring Page
		 * COMPONENT: Sidebar navigation (layout component)
		 *
		 * Navigates from the chat center to the monitoring page using the sidebar
		 * navigation menu. The monitoring page contains the ticket table and filtering
		 * functionality that will be tested.
		 *
		 * NAVIGATION TARGET: /monitoring (+page.svelte)
		 */
		console.log('Phase 2: Navigating to Tickets page via sidebar');

		// Locate and click "Tickets" menu item in sidebar
		const ticketsMenuItem = page.locator('a[href="/monitoring"]');
		await expect(ticketsMenuItem).toBeVisible();
		await ticketsMenuItem.click();
		await page.waitForTimeout(1000); // Delay after navigation

		// Verify navigation to monitoring page
		await expect(page).toHaveURL('/monitoring');
		console.log('Successfully navigated to /monitoring');

		/**
		 * PHASE 3: Monitoring Page Verification
		 * COMPONENT: /monitoring (+page.svelte)
		 *
		 * Verifies that the monitoring page has loaded correctly and contains
		 * the expected UI elements for ticket management.
		 *
		 * ID REFERENCES FROM /monitoring (+page.svelte):
		 * - #page-title (line 329) - Main page title "Tickets"
		 * - #ticket-table-container (line 483) - Container for TicketTable component
		 *
		 * COMPONENT REFERENCES:
		 * - TicketTable.svelte - Embedded ticket table component
		 */
		console.log('Phase 3: Verifying monitoring page loaded correctly');

		// Verify page title using ID selector from monitoring +page.svelte
		await expect(page.locator('#page-title')).toBeVisible();

		// Verify ticket table container is present
		await expect(page.locator('#ticket-table-container')).toBeVisible();

		// Wait for initial ticket data to load
		await page.waitForTimeout(2000); // Allow time for tickets to load

		/**
		 * PHASE 4: Current User Identification
		 * COMPONENT: Sidebar user profile (layout component)
		 *
		 * Extracts the current user's name from the sidebar user profile display.
		 * This information is crucial for validating that the "View My Tickets"
		 * filter correctly shows only tickets assigned to the logged-in user.
		 *
		 * ID REFERENCES FROM Sidebar Component:
		 * - #currentUserName - User profile name display in sidebar
		 *
		 * LANGUAGE-AGNOSTIC APPROACH:
		 * Uses element visibility and content extraction rather than text matching
		 * to ensure compatibility across different UI languages.
		 */
		console.log('Phase 4: Identifying current user from sidebar');

		// Extract current user name from sidebar user profile display
		const userProfileName = page.locator('#currentUserName');
		await expect(userProfileName).toBeVisible();
		const currentUserName = await userProfileName.textContent();
		console.log(`Current logged-in user: ${currentUserName}`);

		// Ensure we have a valid user name
		expect(currentUserName).toBeTruthy();
		expect(currentUserName?.trim()).not.toBe('');

		/**
		 * PHASE 5: Initial Ticket State Capture (Before Filtering)
		 * COMPONENT: TicketTable.svelte
		 *
		 * Captures the initial state of the ticket table before applying any filters.
		 * This baseline data is used to verify that the filtering functionality
		 * correctly reduces the ticket count and shows only relevant tickets.
		 *
		 * ID REFERENCES FROM TicketTable.svelte:
		 * - #ticket-table-body (line 168) - Table body container
		 * - #ticket-row-{ticket.id} (line 177) - Individual ticket rows
		 * - #ticket-cell-agent-{ticket.id} (line 243) - Agent cell container
		 * - #ticket-agent-name-{ticket.id} (line 244) - Agent name display
		 *
		 * DATA-TESTID FALLBACKS:
		 * - [data-testid="ticket-table-body"] - Table body fallback selector
		 * - [data-testid="ticket-row"] - Ticket row fallback selector
		 * - [data-testid="ticket-agent-name"] - Agent name fallback selector
		 */
		console.log('Phase 5: Capturing initial ticket state before filtering');

		// Wait for table body to fully load using ID selector from TicketTable.svelte
		await page.waitForSelector('#ticket-table-body', { timeout: 10000 });

		// Get initial ticket rows using data-testid for reliability
		const initialTicketRows = page.locator('[data-testid="ticket-row"]');
		const initialTicketCount = await initialTicketRows.count();
		console.log(`Initial ticket count: ${initialTicketCount}`);

		// Capture some initial agent names to verify they include other users
		const initialAgentNames = [];
		for (let i = 0; i < Math.min(5, initialTicketCount); i++) {
			const ticketRow = initialTicketRows.nth(i);

			// Use data-testid selector for agent name (more reliable than column index)
			const agentNameElement = ticketRow.locator('[data-testid="ticket-agent-name"]').first();
			if (await agentNameElement.isVisible()) {
				const agentName = await agentNameElement.textContent();
				if (agentName && agentName.trim() !== '' && agentName.trim() !== '-') {
					initialAgentNames.push(agentName.trim());
				}
			}
		}
		console.log(`Initial agent names: ${initialAgentNames.join(', ')}`);

		/**
		 * PHASE 6: View My Tickets Filter Activation
		 * COMPONENT: /monitoring (+page.svelte)
		 *
		 * Activates the "View My Tickets" filter button and verifies the button
		 * state changes correctly. This button triggers the toggleViewMyTasks()
		 * function which updates the viewMyTasks reactive variable and refetches
		 * tickets with the my_tickets filter parameter.
		 *
		 * ID REFERENCES FROM /monitoring (+page.svelte):
		 * - #view-my-tickets-button (line 333) - View My Tickets toggle button
		 *
		 * DATA-TESTID FALLBACKS:
		 * - [data-testid="view-my-tickets-button"] (line 339) - Button fallback selector
		 *
		 * BUTTON STATE LOGIC:
		 * - Inactive: color="none" with hover:bg-gray-100 border classes
		 * - Active: color="dark" (dark background, white text)
		 *
		 * LANGUAGE-AGNOSTIC APPROACH:
		 * Uses ID selector and CSS class inspection rather than text matching
		 * to ensure compatibility across different UI languages.
		 */
		console.log('Phase 6: Activating View My Tickets filter');

		// Locate "View My Tickets" button using ID selector from monitoring +page.svelte
		const viewMyTicketsButton = page.locator('#view-my-tickets-button');
		await expect(viewMyTicketsButton).toBeVisible();

		// Check if button is already active (shouldn't be initially)
		const initialButtonClasses = await viewMyTicketsButton.getAttribute('class');
		console.log(`Initial button state: ${initialButtonClasses}`);

		// Click the "View My Tickets" button to enable filtering
		await viewMyTicketsButton.click();
		await page.waitForTimeout(1000); // Delay after filter activation

		// Verify button state changes to active using language-agnostic class inspection
		const activeButtonClasses = await viewMyTicketsButton.getAttribute('class');
		console.log(`Active button state: ${activeButtonClasses}`);
		expect(activeButtonClasses).toContain('dark'); // Button should show active state

		/**
		 * PHASE 7: Filtered Ticket Data Validation
		 * COMPONENT: TicketTable.svelte
		 *
		 * Validates that the filtered ticket data contains only tickets assigned
		 * to the current user. This phase verifies the core functionality of the
		 * "View My Tickets" filter by examining each ticket's agent assignment.
		 *
		 * ID REFERENCES FROM TicketTable.svelte:
		 * - #ticket-row-{ticket.id} (line 177) - Individual ticket rows
		 * - #ticket-cell-id-{ticket.id} (line 178) - Ticket ID cell
		 * - #ticket-cell-agent-{ticket.id} (line 243) - Agent cell container
		 * - #ticket-agent-name-{ticket.id} (line 244) - Agent name display
		 *
		 * DATA-TESTID FALLBACKS:
		 * - [data-testid="ticket-row"] - Ticket row fallback selector
		 * - [data-testid="ticket-cell-id"] - Ticket ID cell fallback
		 * - [data-testid="ticket-agent-name"] - Agent name fallback selector
		 *
		 * LANGUAGE-AGNOSTIC VALIDATION:
		 * Uses case-insensitive string comparison for agent names to handle
		 * potential variations in capitalization across different languages.
		 */
		console.log('Phase 7: Validating filtered ticket data');

		// Wait for table to refresh with filtered data
		await page.waitForTimeout(2000); // Allow time for filtering to complete

		// Get filtered ticket rows using data-testid for reliability
		const filteredTicketRows = page.locator('[data-testid="ticket-row"]');
		const filteredTicketCount = await filteredTicketRows.count();
		console.log(`Filtered ticket count: ${filteredTicketCount}`);

		// Ensure we have some tickets to validate (assuming test user has tickets)
		expect(filteredTicketCount).toBeGreaterThan(0);

		// Validate each visible ticket belongs to the current user
		const filteredAgentNames = [];
		const mismatchedTickets = [];

		for (let i = 0; i < filteredTicketCount; i++) {
			const ticketRow = filteredTicketRows.nth(i);

			// Extract ticket ID for reference using data-testid
			const ticketIdElement = ticketRow.locator('[data-testid="ticket-cell-id"]').first();
			const ticketId = await ticketIdElement.textContent();

			// Extract Agent name using data-testid selector (more reliable than column index)
			const agentNameElement = ticketRow.locator('[data-testid="ticket-agent-name"]').first();

			await expect(agentNameElement).toBeVisible();
			const agentName = await agentNameElement.textContent();
			const cleanAgentName = agentName?.trim() || '';

			filteredAgentNames.push(cleanAgentName);

			// Assert that Agent matches current user (case-insensitive comparison)
			if (cleanAgentName.toLowerCase() !== currentUserName?.toLowerCase().trim()) {
				mismatchedTickets.push({
					ticketId: ticketId?.trim(),
					agentName: cleanAgentName,
					expectedUser: currentUserName?.trim()
				});
			}

			console.log(`Ticket ${ticketId?.trim()}: Agent = "${cleanAgentName}"`);
		}

		// Assert no mismatched tickets found
		if (mismatchedTickets.length > 0) {
			console.error('Mismatched tickets found:', mismatchedTickets);
			throw new Error(
				`Found ${mismatchedTickets.length} tickets not belonging to current user: ${JSON.stringify(mismatchedTickets)}`
			);
		}

		console.log(`All ${filteredTicketCount} filtered tickets belong to user: ${currentUserName}`);

		/**
		 * PHASE 8: Comprehensive Filter Validation and State Verification
		 *
		 * Performs comprehensive assertions to validate the filtering functionality
		 * and verify that the UI state remains consistent after filtering.
		 *
		 * VALIDATION STRATEGY:
		 * 1. Unique agent verification - ensures only current user tickets are shown
		 * 2. Ticket count reduction verification - confirms filtering actually occurred
		 * 3. Button state persistence - verifies UI state remains consistent
		 * 4. Filter toggle functionality - tests on/off behavior
		 *
		 * LANGUAGE-AGNOSTIC APPROACH:
		 * Uses data structure analysis and CSS class inspection rather than
		 * text-based assertions to ensure cross-language compatibility.
		 */
		console.log('Phase 8: Performing comprehensive assertions');

		// Verify all displayed tickets belong to authenticated user
		const uniqueFilteredAgents = [
			...new Set(filteredAgentNames.filter((name) => name !== '' && name !== '-'))
		];
		expect(uniqueFilteredAgents.length).toBeLessThanOrEqual(1); // Should only have current user or be empty
		if (uniqueFilteredAgents.length === 1) {
			expect(uniqueFilteredAgents[0].toLowerCase()).toBe(currentUserName?.toLowerCase().trim());
		}

		// Verify filter reduced the ticket count (assuming there were tickets from other users initially)
		if (initialAgentNames.length > 0) {
			const hasOtherUsers = initialAgentNames.some(
				(name) => name.toLowerCase() !== currentUserName?.toLowerCase().trim()
			);
			if (hasOtherUsers) {
				expect(filteredTicketCount).toBeLessThanOrEqual(initialTicketCount);
				console.log(
					`Filter successfully reduced tickets from ${initialTicketCount} to ${filteredTicketCount}`
				);
			}
		}

		// Verify filter button remains in active state using language-agnostic class inspection
		const finalButtonClasses = await viewMyTicketsButton.getAttribute('class');
		expect(finalButtonClasses).toContain('dark');

		/**
		 * PHASE 9: Filter Toggle Off Verification
		 *
		 * Tests the filter toggle functionality by deactivating the "View My Tickets"
		 * filter and verifying that the ticket list returns to its unfiltered state.
		 * This ensures the filter can be properly toggled on and off.
		 *
		 * VERIFICATION POINTS:
		 * 1. Button state returns to inactive (no 'dark' class)
		 * 2. Ticket count returns to original or increases
		 * 3. UI state consistency is maintained
		 */
		console.log('Testing filter toggle off...');
		await viewMyTicketsButton.click();
		await page.waitForTimeout(1000);

		// Verify button returns to inactive state using language-agnostic class inspection
		const inactiveButtonClasses = await viewMyTicketsButton.getAttribute('class');
		expect(inactiveButtonClasses).not.toContain('dark');

		// Verify ticket count returns to original or increases
		await page.waitForTimeout(2000);
		const restoredTicketRows = page.locator('[data-testid="ticket-row"]');
		const restoredTicketCount = await restoredTicketRows.count();
		expect(restoredTicketCount).toBeGreaterThanOrEqual(filteredTicketCount);

		console.log(`Test completed successfully! Filter functionality working as expected.`);
		console.log(`- Initial tickets: ${initialTicketCount}`);
		console.log(`- Filtered tickets: ${filteredTicketCount}`);
		console.log(`- Restored tickets: ${restoredTicketCount}`);
		console.log(`- Current user: ${currentUserName}`);
	});

	/**
	 * Test Case: Empty Filter Results Handling
	 *
	 * This test verifies that the application gracefully handles scenarios where
	 * the "View My Tickets" filter returns no results (i.e., when the current user
	 * has no assigned tickets). This is important for ensuring a good user experience
	 * in edge cases.
	 *
	 * COMPONENTS TESTED:
	 * - /monitoring (+page.svelte) - Page-level empty state handling
	 * - TicketTable.svelte - Table-level empty state display
	 *
	 * SCENARIOS COVERED:
	 * 1. User with no assigned tickets (empty filter results)
	 * 2. User with assigned tickets (normal filter results)
	 *
	 * LANGUAGE-AGNOSTIC APPROACH:
	 * Uses structural validation (row counts, element visibility) rather than
	 * text-based assertions to ensure compatibility across different UI languages.
	 */
	test('should handle empty filter results gracefully', async ({ page }) => {
		/**
		 * STEP 1: Direct Navigation to Monitoring Page
		 * COMPONENT: /monitoring (+page.svelte)
		 *
		 * Navigates directly to the monitoring page to test the filter functionality
		 * in isolation, bypassing the sidebar navigation used in the main test.
		 */
		await page.goto('/monitoring');
		await expect(page).toHaveURL('/monitoring');
		await page.waitForTimeout(1000);

		/**
		 * STEP 2: Page Load Verification
		 * COMPONENT: /monitoring (+page.svelte)
		 *
		 * ID REFERENCES FROM /monitoring (+page.svelte):
		 * - #page-title (line 329) - Main page title verification
		 */
		await expect(page.locator('#page-title')).toBeVisible();

		/**
		 * STEP 3: Current User Identification
		 * COMPONENT: Sidebar user profile (layout component)
		 *
		 * ID REFERENCES FROM Sidebar Component:
		 * - #currentUserName - User profile name display
		 */
		const userProfileName = page.locator('#currentUserName');
		const currentUserName = await userProfileName.textContent();

		/**
		 * STEP 4: Filter Activation
		 * COMPONENT: /monitoring (+page.svelte)
		 *
		 * ID REFERENCES FROM /monitoring (+page.svelte):
		 * - #view-my-tickets-button (line 333) - View My Tickets toggle button
		 */
		const viewMyTicketsButton = page.locator('#view-my-tickets-button');
		await viewMyTicketsButton.click();
		await page.waitForTimeout(2000);

		/**
		 * STEP 5: Empty State and Normal State Handling
		 * COMPONENT: TicketTable.svelte
		 *
		 * DATA-TESTID REFERENCES FROM TicketTable.svelte:
		 * - [data-testid="ticket-row"] - Individual ticket rows
		 * - [data-testid="no-tickets-row"] (line 170) - Empty state row
		 * - [data-testid="ticket-agent-name"] - Agent name elements
		 *
		 * LANGUAGE-AGNOSTIC VALIDATION:
		 * Uses row count and element structure rather than text content
		 * to validate both empty and populated states.
		 */
		const ticketRows = page.locator('[data-testid="ticket-row"]');
		const rowCount = await ticketRows.count();

		if (rowCount === 0) {
			// Verify empty state is handled gracefully
			console.log('No tickets found for current user - empty state handled correctly');
			expect(rowCount).toBe(0);

			// Optionally verify empty state message is displayed
			const emptyStateRow = page.locator('[data-testid="no-tickets-row"]');
			if (await emptyStateRow.isVisible()) {
				console.log('Empty state row is properly displayed');
			}
		} else {
			// If tickets exist, verify they belong to current user using language-agnostic validation
			console.log(`Found ${rowCount} tickets for current user - validating ownership`);
			for (let i = 0; i < rowCount; i++) {
				const ticketRow = ticketRows.nth(i);
				const agentNameElement = ticketRow.locator('[data-testid="ticket-agent-name"]').first();
				const agentName = await agentNameElement.textContent();
				expect(agentName?.toLowerCase().trim()).toBe(currentUserName?.toLowerCase().trim());
			}
			console.log('All tickets verified to belong to current user');
		}
	});
});
